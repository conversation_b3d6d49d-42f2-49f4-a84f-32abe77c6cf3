import axios from 'axios';
import { useCallback } from 'react';
import { toast } from 'sonner';

interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export function useApiErrorHandler() {
  const handleError = useCallback((error: any, customMessage?: string): ApiError => {
    let errorMessage = customMessage || 'An unexpected error occurred';
    let status: number | undefined;
    let code: string | undefined;
    let details: any;

    if (axios.isAxiosError(error)) {
      status = error.response?.status;
      code = error.code;
      details = error.response?.data;

      // Handle specific HTTP status codes
      switch (status) {
        case 400:
          errorMessage = error.response?.data?.message || 'Invalid request. Please check your input.';
          break;
        case 401:
          errorMessage = 'Authentication required. Please log in again.';
          // Redirect to login
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          errorMessage = 'Access denied. You don\'t have permission to perform this action.';
          break;
        case 404:
          errorMessage = 'Resource not found. The requested item may have been deleted.';
          break;
        case 409:
          errorMessage = error.response?.data?.message || 'Conflict. This action cannot be completed.';
          break;
        case 422:
          errorMessage = error.response?.data?.message || 'Validation error. Please check your input.';
          break;
        case 429:
          errorMessage = 'Too many requests. Please wait a moment and try again.';
          break;
        case 500:
          errorMessage = 'Server error. Please try again later.';
          break;
        case 502:
        case 503:
        case 504:
          errorMessage = 'Service temporarily unavailable. Please try again later.';
          break;
        default:
          errorMessage = error.response?.data?.message || error.message || errorMessage;
      }

      // Handle network errors
      if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
        errorMessage = 'Network error. Please check your internet connection.';
      }

      // Handle timeout errors
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timeout. Please try again.';
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    // Show toast notification
    toast.error(errorMessage);

    // Log error for debugging (in development)
    if (import.meta.env.DEV) {
      console.error('API Error:', {
        message: errorMessage,
        status,
        code,
        details,
        originalError: error
      });
    }

    return {
      message: errorMessage,
      status,
      code,
      details
    };
  }, []);

  const handleSuccess = useCallback((message: string, data?: any) => {
    toast.success(message);

    if (import.meta.env.DEV && data) {
      console.log('API Success:', { message, data });
    }
  }, []);

  const handleWarning = useCallback((message: string) => {
    toast.warning(message);
  }, []);

  const handleInfo = useCallback((message: string) => {
    toast.info(message);
  }, []);

  return {
    handleError,
    handleSuccess,
    handleWarning,
    handleInfo
  };
}

// Utility function to extract error message from various error formats
export function extractErrorMessage(error: any): string {
  if (axios.isAxiosError(error)) {
    return error.response?.data?.message ||
           error.response?.data?.error ||
           error.message ||
           'Network error occurred';
  }

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return 'An unexpected error occurred';
}

// Utility function to check if error is a network error
export function isNetworkError(error: any): boolean {
  return axios.isAxiosError(error) &&
         (error.code === 'NETWORK_ERROR' ||
          error.code === 'ERR_NETWORK' ||
          !error.response);
}

// Utility function to check if error is a timeout error
export function isTimeoutError(error: any): boolean {
  return axios.isAxiosError(error) && error.code === 'ECONNABORTED';
}

// Utility function to check if error is a server error (5xx)
export function isServerError(error: any): boolean {
  return axios.isAxiosError(error) &&
         error.response?.status &&
         error.response.status >= 500;
}

// Utility function to check if error is a client error (4xx)
export function isClientError(error: any): boolean {
  return axios.isAxiosError(error) &&
         error.response?.status &&
         error.response.status >= 400 &&
         error.response.status < 500;
}

export default useApiErrorHandler;
