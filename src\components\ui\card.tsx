
import * as React from "react"

import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-2xl border-[1.5px] border-border bg-card text-card-foreground shadow-kola transition-all duration-500 hover:shadow-glass-strong hover:-translate-y-2 group relative overflow-hidden dark:border-green-500/40 dark:bg-card",
      "before:absolute before:inset-0 before:bg-gradient-to-r before:from-brand-blue/0 before:to-brand-blue/10 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-700",
      "after:absolute after:bottom-0 after:left-0 after:h-1 after:w-0 after:bg-brand-yellow hover:after:w-full after:transition-all after:duration-500",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex flex-col space-y-1.5 p-4 sm:p-6 relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-brand-yellow after:to-transparent group-hover:after:opacity-100 after:opacity-0 after:transition-all after:duration-500",
      className
    )}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight group-hover:text-brand-blue transition-colors duration-300 text-[0.95em]",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground group-hover:text-muted-foreground/80 transition-colors duration-300 text-[0.95em]", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 sm:p-6 pt-0 relative z-10", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex items-center p-4 sm:p-6 pt-0 group-hover:before:animate-yellow-pulse relative before:absolute before:inset-0 before:rounded-b-2xl before:content-[''] before:opacity-0 group-hover:before:opacity-30 before:transition-opacity before:duration-500",
      className
    )}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle }
