// src/App.tsx
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Suspense, lazy, useEffect } from "react";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";

import { LoadingScreen } from "@/components/ui/loading-screen";
import { OfflineIndicator } from "@/components/ui/offline-indicator";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

import { EnvValidator } from "@/components/EnvValidator";
import ErrorBoundary from "@/components/ErrorBoundary";
import { useAOS } from "@/hooks/use-aos";
import { AuthProvider } from "@/hooks/use-auth";
import { NotificationProvider } from "@/hooks/use-notifications";
import { ThemeProvider } from "@/hooks/use-theme";

// Lazy-loaded pages and layouts
const Login = lazy(() => import("./pages/Login"));
const Signup = lazy(() => import("./pages/Signup"));
const NotFound = lazy(() => import("./pages/NotFound"));

const UserDashboard = lazy(() => import("./pages/user/Dashboard"));
const SavingsPlans = lazy(() => import("./pages/user/SavingsPlans"));
const Payments = lazy(() => import("./pages/user/Payments"));
const KycVerification = lazy(() => import("./pages/user/KycVerification"));
const Transactions = lazy(() => import("./pages/user/Transactions"));
const Analytics = lazy(() => import("./pages/user/Analytics"));
const Settings = lazy(() => import("./pages/user/Settings"));
const AddCard = lazy(() => import("./pages/user/AddCard"));

const AdminDashboard = lazy(() => import("./pages/admin/Dashboard"));
const UserManagement = lazy(() => import("./pages/admin/UserManagement"));
const UserProfileDetails = lazy(() => import("./pages/admin/UserProfileDetails"));
const UserRequests = lazy(() => import("./pages/admin/Requests"));
const AdminAnalytics = lazy(() => import("./pages/admin/Analytics"));
const OtpVerificationManagement = lazy(() => import("./pages/admin/OtpVerificationManagement"));
const AssignPlanToUser = lazy(() => import("./pages/admin/AssignPlanToUser"));
const AdminRolesManagement = lazy(() => import("./pages/admin/AdminRolesManagement"));
const StaffManagement = lazy(() => import("./pages/admin/StaffManagement"));
const GroupSavingsPlans = lazy(() => import("./pages/admin/GroupSavingsPlans"));
const NotificationManagement = lazy(() => import("./pages/admin/NotificationManagement"));
const StaffRoleManagement = lazy(() => import("./pages/admin/StaffRoleManagement"));

const UserLayout = lazy(() => import("./components/layout/user-layout"));

// Query Client Configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 60 * 60 * 1000,
      retry: (failureCount, error: unknown) => {
        const errorWithStatus = error as { status?: number };
        if (!navigator.onLine || errorWithStatus?.status === 404) return false;
        return failureCount < 3;
      },
    },
  },
});

// Root Application
const App = () => {
  return (
    <ErrorBoundary>
      <EnvValidator>
        <QueryClientProvider client={queryClient}>
          <BrowserRouter>
            <TooltipProvider>
              <ThemeProvider>
                <AuthProvider>
                  <NotificationProvider>
                    <AppContent />
                  </NotificationProvider>
                </AuthProvider>
              </ThemeProvider>
            </TooltipProvider>
          </BrowserRouter>
        </QueryClientProvider>
      </EnvValidator>
    </ErrorBoundary>
  );
};

// Application Routes and Layouts
const AppContent = () => {
  // Initialize AOS animations
  useAOS();

  useEffect(() => {
    document.title = "Better Interest | Secure Digital Savings";
  }, []);

  return (
    <>
      <Toaster />
      <Sonner />
      <OfflineIndicator />
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* User Layout */}
          <Route element={<UserLayout isAdmin={false} />}>
            <Route path="/dashboard" element={<UserDashboard />} />
            <Route path="/savings" element={<SavingsPlans />} />
            <Route path="/payments" element={<Payments />} />
            <Route path="/kyc" element={<KycVerification />} />
            <Route path="/transactions" element={<Transactions />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/add-card" element={<AddCard />} />
            <Route path="/profile" element={<Navigate to="/settings" replace />} />
          </Route>

          {/* Admin Layout */}
          <Route element={<UserLayout isAdmin={true} />}>
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            <Route path="/admin/users" element={<UserManagement />} />
            <Route path="/admin/users/:userId" element={<UserProfileDetails />} />
            <Route path="/admin/settings" element={<Settings />} />
            <Route path="/admin/requests" element={<UserRequests />} />
            <Route path="/admin/analytics" element={<AdminAnalytics />} />
            <Route path="/admin/payment-management" element={<Navigate to="/admin/requests" replace />} />
            <Route path="/admin/verification" element={<OtpVerificationManagement />} />
            <Route path="/admin/assign-plan" element={<AssignPlanToUser />} />
            <Route path="/admin/roles" element={<AdminRolesManagement />} />
            <Route path="/admin/staff" element={<StaffManagement />} />
            <Route path="/admin/group-savings" element={<GroupSavingsPlans />} />
            <Route path="/admin/notifications" element={<NotificationManagement />} />
            <Route path="/admin/staff-roles" element={<StaffRoleManagement />} />
          </Route>

          {/* Not Found */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </>
  );
};

export default App;
