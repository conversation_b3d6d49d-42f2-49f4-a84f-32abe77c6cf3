import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FloatingLabelInput } from "@/components/ui/floating-label-input";
import { useAuth } from '@/hooks/use-auth';
import { useAuthApi } from '@/hooks/use-auth-api';
import { useTheme } from '@/hooks/use-theme';
import { validateEmail, validatePassword } from '@/utils/form-validation';
import { Fingerprint, Lock, Monitor, Moon, Sun } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const navigate = useNavigate();
  const { signIn } = useAuth();
  const { login, isLoading } = useAuthApi();
  const { theme, setTheme } = useTheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<{email?: string; password?: string}>({});

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    if (emailError || passwordError) {
      setErrors({
        email: emailError || undefined,
        password: passwordError || undefined,
      });
      return;
    }

    setErrors({});

    try {
      const result = await login({ email, password });
      if (result.data) {
        // Login successful, navigate to dashboard
        navigate('/dashboard');
      }
    } catch (error: any) {
      console.error("Login error:", error);
    }
  };

  const handleCreateAccount = () => {
    navigate('/signup');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-orange-50 bla:from-gray-900 black:via-gray-800 black:to-gray-900 flex items-center justify-center p-4 relative">
      {/* Theme Switcher */}
      <div className="absolute top-4 right-4 z-10">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setTheme("light")}>
              <Sun className="mr-2 h-4 w-4" />
              <span>Light</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("dark")}>
              <Moon className="mr-2 h-4 w-4" />
              <span>Dark</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("system")}>
              <Monitor className="mr-2 h-4 w-4" />
              <span>System</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="w-full max-w-sm mx-auto">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="h-32 w-32 rounded-full overflow-hidden shadow-2xl bg-white/10 backdrop-blur-sm border border-white/20 p-4">
                <img
                  src="/logo-white-circular.svg"
                  alt="Better Interest Logo"
                  className="h-full w-full object-contain"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center gap-2 mb-6">
            <Fingerprint className="h-5 w-5 text-green-600" />
            <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">Secure • Fast • Reliable</span>
          </div>
        </div>

        {/* Login Card */}
        <Card className="w-full shadow-xl border-0 bg-white/80 black:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">Welcome Back</CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300">
              Sign in to continue to your dashboard
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div>
                <FloatingLabelInput
                  id="email"
                  type="email"
                  label="Email Address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <FloatingLabelInput
                  id="password"
                  type="password"
                  label="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full"
                />
                {errors.password && (
                  <p className="text-red-500 text-sm mt-1">{errors.password}</p>
                )}
              </div>

              <div className="flex justify-center pt-4">
                <Button
                  type="submit"
                  className="w-full max-w-xs bg-green-600 hover:bg-green-700 text-white font-semibold py-3 rounded-lg transition-all duration-200 transform hover:scale-105"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="animate-spin mr-2">⟳</span>
                      Signing in...
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 mr-2" />
                      Sign In
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Don't have an account?{" "}
                <button
                  onClick={handleCreateAccount}
                  className="text-green-600 hover:text-green-700 hover:underline font-medium transition-colors"
                >
                  Create Account
                </button>
              </p>
            </div>

            {/* Small images at bottom */}
            <div className="flex justify-center items-center gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <img
                  src="/lovable-uploads/CBN_NDIC.webp"
                  alt="Better Interest"
                  className="h-8 w-8 object-contain opacity-80"
                />
                <img
                  src="/lovable-uploads/7fb4a757-e34b-466b-b00e-1df181ec4f0b.png"
                  alt="Secure Banking"
                  className="h-8 w-8 object-contain opacity-80"
                />
              </div>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Protected by advanced encryption and secure authentication
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
