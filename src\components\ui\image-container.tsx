import { cn } from "@/lib/utils";

interface ImageContainerProps {
  src: string;
  alt: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  imageClassName?: string;
}

const sizeClasses = {
  sm: "w-8 h-8",
  md: "w-12 h-12", 
  lg: "w-16 h-16"
};

const imageSizeClasses = {
  sm: "w-5 h-5",
  md: "w-8 h-8",
  lg: "w-12 h-12"
};

export function ImageContainer({ 
  src, 
  alt, 
  size = "md", 
  className,
  imageClassName 
}: ImageContainerProps) {
  return (
    <div 
      className={cn(
        "flex items-center justify-center bg-white/10 dark:bg-gray-800/50 rounded-[15px] border border-gray-200/50 dark:border-gray-600/50 backdrop-blur-sm transition-all duration-200 hover:bg-white/20 dark:hover:bg-gray-700/60",
        sizeClasses[size],
        className
      )}
    >
      <img
        src={src}
        alt={alt}
        className={cn(
          "object-contain opacity-90",
          imageSizeClasses[size],
          imageClassName
        )}
      />
    </div>
  );
}
