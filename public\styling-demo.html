<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Styling Demo - Better Interest</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1e3a8a, #1e40af, #3b82f6);
            min-height: 100vh;
            color: white;
            font-family: system-ui, -apple-system, sans-serif;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
        }
        
        .form-input {
            height: 3rem;
            width: 100%;
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem 1rem;
            color: white;
            transition: all 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: rgba(34, 197, 94, 0.5);
            box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
        }
        
        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .demo-button {
            background: linear-gradient(to bottom, #22c55e, #16a34a, #15803d);
            color: white;
            border: 2px solid rgba(34, 197, 94, 0.2);
            border-radius: 1rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .demo-button:hover {
            transform: scale(0.98);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.25), inset 0 -1px 0 rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .demo-button:active {
            transform: scale(0.96);
            box-shadow: inset 0 4px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.05);
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .logo-circle {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-circle img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .small-logo {
            width: 1.5rem;
            height: 1.5rem;
        }
        
        .medium-logo {
            width: 2.5rem;
            height: 2.5rem;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <div class="text-center mb-8">
            <div class="logo-container justify-center">
                <div class="logo-circle">
                    <img src="/logo-white-circular.svg" alt="Better Interest Logo">
                </div>
                <h1 class="text-3xl font-bold">Better Interest</h1>
            </div>
            <p class="text-lg opacity-90">Updated Styling Demo</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4">✅ Completed Updates</h2>
            <ul class="space-y-2 text-sm">
                <li>🔄 <strong>Server restarted</strong> - Running on http://localhost:3000/</li>
                <li>📏 <strong>Logo sizes reduced</strong> - Sidebar: 24px, Login: 80px, Signup: 40px</li>
                <li>🔘 <strong>Border radius increased</strong> - All buttons now use rounded-2xl (16px)</li>
                <li>📝 <strong>Form fields enhanced</strong> - Inputs, textareas, selects with rounded-xl (12px)</li>
                <li>🎨 <strong>Cards updated</strong> - All cards now use rounded-2xl</li>
                <li>⚡ <strong>Transitions added</strong> - Smooth hover and focus effects</li>
            </ul>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4">🎨 Logo Size Comparison</h2>
            <div class="flex items-center gap-4 flex-wrap">
                <div class="text-center">
                    <div class="logo-circle small-logo mx-auto mb-2">
                        <img src="/logo-white-circular.svg" alt="Small Logo">
                    </div>
                    <p class="text-xs">Sidebar (24px)</p>
                </div>
                <div class="text-center">
                    <div class="logo-circle medium-logo mx-auto mb-2">
                        <img src="/logo-white-circular.svg" alt="Medium Logo">
                    </div>
                    <p class="text-xs">Signup (40px)</p>
                </div>
                <div class="text-center">
                    <div class="logo-circle mx-auto mb-2">
                        <img src="/logo-white-circular.svg" alt="Large Logo">
                    </div>
                    <p class="text-xs">Login (80px)</p>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4">🔘 Button Styles</h2>
            <div class="flex gap-3 flex-wrap">
                <button class="demo-button">Primary Button</button>
                <button class="demo-button" style="background: linear-gradient(to bottom, #ef4444, #dc2626, #b91c1c);">Destructive</button>
                <button class="demo-button" style="background: linear-gradient(to bottom, #6b7280, #4b5563, #374151);">Secondary</button>
            </div>
            <p class="text-xs mt-3 opacity-75">All buttons now use rounded-2xl (16px radius) with enhanced 3D effects</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4">📝 Form Elements</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Input Field</label>
                    <input type="text" class="form-input" placeholder="Enter your text here...">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Textarea</label>
                    <textarea class="form-input" rows="3" placeholder="Enter your message..."></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Select Dropdown</label>
                    <select class="form-input">
                        <option>Choose an option...</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </div>
            <p class="text-xs mt-3 opacity-75">All form elements use rounded-xl (12px radius) with smooth transitions</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4">📱 Responsive Design</h2>
            <p class="text-sm opacity-90 mb-3">The updated styling maintains full responsiveness:</p>
            <ul class="text-xs space-y-1 opacity-75">
                <li>• Mobile-first approach with touch-friendly sizes</li>
                <li>• Consistent spacing and proportions across devices</li>
                <li>• Smooth animations and transitions</li>
                <li>• Accessible focus states and hover effects</li>
            </ul>
        </div>

        <div class="text-center mt-8">
            <p class="text-sm opacity-75">
                🚀 Your app is now running with enhanced styling!<br>
                Visit <a href="http://localhost:3000/" class="underline hover:text-green-300">http://localhost:3000/</a> to see the changes.
            </p>
        </div>
    </div>
</body>
</html>
