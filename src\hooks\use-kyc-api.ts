import { API_CONFIG } from '@/config/env';
import { useCallback, useState } from 'react';
import { useApiErrorHandler } from './use-api-error-handler';

interface KycFormData {
  fullName: string;
  phoneNumber: string;
  email: string;
  address: string;
  city: string;
  state: string;
  idType: string;
  idNumber: string;
  idImage: File | null;
  proofOfAddressImage: File | null;
}

interface KycStatus {
  _id: string;
  userId: string;
  status: 'NOT_SUBMITTED' | 'PENDING' | 'APPROVED' | 'REJECTED';
  kycType: 'BVN' | 'NIN' | null;
  submittedAt?: string;
  reviewedAt?: string;
  rejectionReason?: string;
}

const API_URL = API_CONFIG.BASE_URL;

export function useKycApi() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { handleError, handleSuccess } = useApiErrorHandler();

  // Helper to get token from localStorage
  const getToken = () => localStorage.getItem('token');

  // Submit KYC verification
  const submitKyc = useCallback(async (formData: KycFormData) => {
    setIsSubmitting(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      // Create FormData for file upload
      const submitData = new FormData();
      
      // Append text fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key !== 'idImage' && key !== 'proofOfAddressImage' && value) {
          submitData.append(key, value as string);
        }
      });

      // Append files
      if (formData.idImage) {
        submitData.append('idImage', formData.idImage);
      }
      if (formData.proofOfAddressImage) {
        submitData.append('proofOfAddressImage', formData.proofOfAddressImage);
      }

      const response = await fetch(`${API_URL}/kyc/submit`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
          // Don't set Content-Type for FormData, let browser set it
        },
        body: submitData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit KYC');
      }

      const result = await response.json();
      handleSuccess('KYC verification submitted successfully! We will review your documents within 24-48 hours.');
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error submitting KYC:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit KYC verification';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  }, [handleError, handleSuccess]);

  // Get KYC status
  const getKycStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/kyc/status`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch KYC status');
      }

      const result = await response.json();
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error fetching KYC status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch KYC status';
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Verify NIN (if using Dojah integration)
  const verifyNin = useCallback(async (nin: string) => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/kyc/verify-nin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ nin })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to verify NIN');
      }

      const result = await response.json();
      handleSuccess('NIN verified successfully!');
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error verifying NIN:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to verify NIN';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Get all KYC submissions (admin only)
  const getAllKycSubmissions = useCallback(async (status?: string, page = 1, limit = 20) => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });
      
      if (status) {
        params.append('status', status);
      }

      const response = await fetch(`${API_URL}/kyc/admin/submissions?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch KYC submissions');
      }

      const result = await response.json();
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error fetching KYC submissions:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch KYC submissions';
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Approve KYC (admin only)
  const approveKyc = useCallback(async (kycId: string) => {
    setIsSubmitting(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/kyc/admin/approve/${kycId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to approve KYC');
      }

      const result = await response.json();
      handleSuccess('KYC approved successfully!');
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error approving KYC:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to approve KYC';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  }, [handleError, handleSuccess]);

  // Reject KYC (admin only)
  const rejectKyc = useCallback(async (kycId: string, reason: string) => {
    setIsSubmitting(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/kyc/admin/reject/${kycId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reject KYC');
      }

      const result = await response.json();
      handleSuccess('KYC rejected successfully!');
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Error rejecting KYC:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to reject KYC';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  }, [handleError, handleSuccess]);

  return {
    isLoading,
    isSubmitting,
    submitKyc,
    getKycStatus,
    verifyNin,
    getAllKycSubmissions,
    approveKyc,
    rejectKyc,
  };
}

export default useKycApi;
