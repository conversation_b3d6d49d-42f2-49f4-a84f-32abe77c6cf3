import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTheme } from "@/hooks/use-theme";

const StyleTest = () => {
  const { theme, setTheme } = useTheme();

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 text-primary">Better Interest - Brand Colors Test</h1>
          <p className="text-muted-foreground mb-6">
            Testing Green, Yellow & Orange brand colors - Current theme: <strong>{theme}</strong>
          </p>
          
          <div className="flex gap-4 justify-center mb-8">
            <Button onClick={() => setTheme('light')} variant={theme === 'light' ? 'default' : 'outline'}>
              Light Theme
            </Button>
            <Button onClick={() => setTheme('dark')} variant={theme === 'dark' ? 'default' : 'outline'}>
              Dark Theme
            </Button>
            <Button onClick={() => setTheme('system')} variant={theme === 'system' ? 'default' : 'outline'}>
              System Theme
            </Button>
          </div>
        </div>

        {/* Button Variants Test */}
        <Card>
          <CardHeader>
            <CardTitle>Button Variants - Brand Colors (40px Border Radius)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="text-sm font-medium mb-3 text-primary">Primary Brand Colors</h4>
              <div className="flex flex-wrap gap-3">
                <Button variant="default">Green Primary</Button>
                <Button variant="secondary">Yellow Secondary</Button>
                <Button variant="accent">Orange Accent</Button>
                <Button variant="brand">Brand Green</Button>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-3 text-muted-foreground">Standard Variants</h4>
              <div className="flex flex-wrap gap-3">
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
                <Button variant="destructive">Destructive</Button>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-3 text-muted-foreground">Special Variants</h4>
              <div className="flex flex-wrap gap-3">
                <Button variant="glow">Glow Effect</Button>
                <Button variant="admin">Admin</Button>
                <Button variant="yellow">Yellow Special</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Card Test */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Card 1 (1.5px Borders)</CardTitle>
            </CardHeader>
            <CardContent>
              <p>This card should have 1.5px borders and yellow borders in dark theme.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Card 2</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Testing the new card styling with enhanced borders.</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Card 3</CardTitle>
            </CardHeader>
            <CardContent>
              <p>All cards should have consistent styling.</p>
            </CardContent>
          </Card>
        </div>

        {/* Form Elements Test */}
        <Card>
          <CardHeader>
            <CardTitle>Form Elements - 30px Border Radius (1.5px Borders)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-primary">Input Field (30px radius)</label>
              <Input placeholder="Test input with 30px border radius and 1.5px borders" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-primary">Textarea (30px radius)</label>
              <Textarea placeholder="Test textarea with 30px border radius and 1.5px borders" rows={3} />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-primary">Select Dropdown (30px radius)</label>
              <select className="flex h-12 w-full items-center justify-between rounded-[30px] border-[1.5px] border-input bg-background px-4 py-3 text-base ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transition-all duration-200 focus:border-primary/50 hover:border-primary/30 dark:border-green-500/40 dark:focus:border-green-500/60 dark:bg-input">
                <option>Choose an option...</option>
                <option>Option 1</option>
                <option>Option 2</option>
              </select>
            </div>

            <div className="flex gap-3">
              <Button size="sm">Small Button (40px)</Button>
              <Button size="default">Default Button (40px)</Button>
              <Button size="lg">Large Button (40px)</Button>
            </div>
          </CardContent>
        </Card>

        {/* Brand Color Palette Test */}
        <Card>
          <CardHeader>
            <CardTitle>Brand Color Palette - Green, Yellow & Orange</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-6 rounded-lg bg-primary text-primary-foreground text-center">
                <div className="text-lg font-bold">Primary - Green</div>
                <div className="text-sm opacity-90">Brand Green (#22c55e)</div>
                <div className="text-xs opacity-75">Main brand color</div>
              </div>
              <div className="p-6 rounded-lg bg-secondary text-secondary-foreground text-center">
                <div className="text-lg font-bold">Secondary - Yellow</div>
                <div className="text-sm opacity-90">Brand Yellow (#f59e0b)</div>
                <div className="text-xs opacity-75">Accent & highlights</div>
              </div>
              <div className="p-6 rounded-lg bg-accent text-accent-foreground text-center">
                <div className="text-lg font-bold">Accent - Orange</div>
                <div className="text-sm opacity-90">Orange (#f97316)</div>
                <div className="text-xs opacity-75">Call-to-action</div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 rounded-lg bg-background border border-border">
                <div className="text-sm font-medium">Background</div>
                <div className="text-xs text-muted-foreground">Black in dark theme</div>
              </div>
              <div className="p-4 rounded-lg bg-card border border-border">
                <div className="text-sm font-medium">Cards</div>
                <div className="text-xs text-muted-foreground">Dark green-tinted</div>
              </div>
              <div className="p-4 rounded-lg bg-muted border border-border">
                <div className="text-sm font-medium">Muted</div>
                <div className="text-xs text-muted-foreground">Subtle backgrounds</div>
              </div>
              <div className="p-4 rounded-lg border-[1.5px] border-primary bg-primary/10">
                <div className="text-sm font-medium text-primary">Borders</div>
                <div className="text-xs text-muted-foreground">Green borders</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Border Test */}
        <Card>
          <CardHeader>
            <CardTitle>Border Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border-[1.5px] border-border rounded-lg">
                <p>This div has 1.5px borders like cards</p>
              </div>
              <div className="p-4 border-[1.8px] border-primary rounded-[40px]">
                <p>This div has 1.8px borders with 40px radius like buttons</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StyleTest;
