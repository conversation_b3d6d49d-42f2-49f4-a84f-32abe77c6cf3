import React from 'react';

interface CircularLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  variant?: 'white' | 'colored';
}

const CircularLogo: React.FC<CircularLogoProps> = ({ 
  size = 'md', 
  className = '',
  variant = 'white'
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8', 
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const logoSrc = variant === 'white'
    ? '/logo-white-circular.svg'
    : '/lovable-uploads/96b8da77-302c-4b65-87fb-ec3cf6bc86ca.png';

  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg ${className}`}>
      <img 
        src={logoSrc}
        alt="Better Interest Logo" 
        className="h-full w-full object-contain p-1"
      />
    </div>
  );
};

export default CircularLogo;
