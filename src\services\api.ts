import { API_CONFIG } from '@/config/env';
import axios from 'axios';

const API_URL = API_CONFIG.BASE_URL;

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: API_CONFIG.TIMEOUT,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API Service Class
export class ApiService {
  // Flex Savings API
  static async getFlexAccount() {
    const response = await api.get('/flex-savings/account');
    return response.data;
  }

  static async depositToFlex(amount: number) {
    const response = await api.post('/flex-savings/deposit', { amount });
    return response.data;
  }

  static async withdrawFromFlex(amount: number) {
    const response = await api.post('/flex-savings/withdraw', { amount });
    return response.data;
  }

  static async getFlexInterestHistory(period = 'month') {
    const response = await api.get('/flex-savings/interest-history', {
      params: { period }
    });
    return response.data;
  }

  // Fixed Deposit API
  static async createFixedDeposit(amount: number, duration: number, autoRenewal = false) {
    const response = await api.post('/fixed-deposit/create', {
      amount,
      duration,
      autoRenewal
    });
    return response.data;
  }

  static async getUserFixedDeposits(status?: string) {
    const response = await api.get('/fixed-deposit/user', {
      params: status ? { status } : {}
    });
    return response.data;
  }

  static async breakFixedDeposit(depositId: string, reason?: string) {
    const response = await api.post(`/fixed-deposit/break/${depositId}`, {
      reason
    });
    return response.data;
  }

  static async calculateFixedDepositInterest(amount: number, duration: number) {
    const response = await api.get('/fixed-deposit/calculate-interest', {
      params: { amount, duration }
    });
    return response.data;
  }

  // SafeLock API
  static async createSafeLock(amount: number, lockDuration: number, canBreak = true) {
    const response = await api.post('/safelock/create', {
      amount,
      lockDuration,
      canBreak
    });
    return response.data;
  }

  static async getUserSafeLocks(status?: string) {
    const response = await api.get('/safelock/user', {
      params: status ? { status } : {}
    });
    return response.data;
  }

  static async breakSafeLock(safeLockId: string, reason: string, confirmBreak = true) {
    const response = await api.post(`/safelock/break/${safeLockId}`, {
      reason,
      confirmBreak
    });
    return response.data;
  }

  static async calculateSafeLockReturns(amount: number, duration: number) {
    const response = await api.get('/safelock/calculate-returns', {
      params: { amount, duration }
    });
    return response.data;
  }

  static async getSafeLockRates() {
    const response = await api.get('/safelock/rates');
    return response.data;
  }

  // Card Management API
  static async getUserCards() {
    const response = await api.get('/cards/user');
    return response.data;
  }

  static async addCard(email: string) {
    const response = await api.post('/cards/add', { email });
    return response.data;
  }

  static async verifyCard(reference: string) {
    const response = await api.post('/cards/verify', { reference });
    return response.data;
  }

  static async setDefaultCard(cardId: string) {
    const response = await api.put(`/cards/${cardId}/set-default`);
    return response.data;
  }

  static async removeCard(cardId: string) {
    const response = await api.delete(`/cards/${cardId}`);
    return response.data;
  }

  static async chargeCard(cardId: string, amount: number, description?: string) {
    const response = await api.post('/cards/charge', {
      cardId,
      amount,
      description
    });
    return response.data;
  }

  // AutoSave API
  static async setupAutoSave(data: {
    cardId: string;
    frequency: 'daily' | 'weekly' | 'monthly';
    amount: number;
    targetType?: string;
    targetPlanId?: string;
    preferredTime?: string;
    avoidWeekends?: boolean;
  }) {
    const response = await api.post('/autosave/setup', data);
    return response.data;
  }

  static async getUserAutoSaves() {
    const response = await api.get('/autosave/user');
    return response.data;
  }

  static async toggleAutoSave(autoSaveId: string, action: 'pause' | 'resume' | 'deactivate', duration?: number) {
    const response = await api.put(`/autosave/${autoSaveId}/toggle`, {
      action,
      duration
    });
    return response.data;
  }

  static async updateAutoSave(autoSaveId: string, data: {
    amount?: number;
    frequency?: string;
    preferredTime?: string;
    avoidWeekends?: boolean;
  }) {
    const response = await api.put(`/autosave/${autoSaveId}/update`, data);
    return response.data;
  }

  static async getAutoSaveHistory(autoSaveId: string, page = 1, limit = 20) {
    const response = await api.get(`/autosave/${autoSaveId}/history`, {
      params: { page, limit }
    });
    return response.data;
  }

  // Interest API
  static async getInterestHistory(userId: string, period = 'month', page = 1, limit = 20) {
    const response = await api.get(`/interest/history/${userId}`, {
      params: { period, page, limit }
    });
    return response.data;
  }

  static async getInterestRates() {
    const response = await api.get('/interest/rates');
    return response.data;
  }

  static async getInterestDashboard(userId: string) {
    const response = await api.get(`/interest/dashboard/${userId}`);
    return response.data;
  }

  // Referral API
  static async useReferralCode(referralCode: string, ipAddress?: string, deviceFingerprint?: string) {
    const response = await api.post('/referral/use-code', {
      referralCode,
      ipAddress,
      deviceFingerprint
    });
    return response.data;
  }

  static async getReferralStats(userId: string) {
    const response = await api.get(`/referral/stats/${userId}`);
    return response.data;
  }

  static async getReferralLeaderboard(period = 'all', limit = 10) {
    const response = await api.get('/referral/leaderboard', {
      params: { period, limit }
    });
    return response.data;
  }

  static async getMyReferralCode() {
    const response = await api.get('/referral/my-code');
    return response.data;
  }

  // User Profile API (Enhanced)
  static async getUserProfile() {
    const response = await api.get('/auth/profile');
    return response.data;
  }

  static async updateUserProfile(data: any) {
    const response = await api.put('/auth/profile', data);
    return response.data;
  }

  // Dashboard API
  static async getDashboardData() {
    const response = await api.get('/auth/dashboard');
    return response.data;
  }

  // Transaction API
  static async getUserTransactions(page = 1, limit = 20, type?: string) {
    const response = await api.get('/transactions', {
      params: { page, limit, type }
    });
    return response.data;
  }

  // Utility methods
  static async uploadFile(file: File, type: string) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  static async sendNotification(userId: string, title: string, message: string) {
    const response = await api.post('/notifications/send', {
      userId,
      title,
      message
    });
    return response.data;
  }
}

export default ApiService;
