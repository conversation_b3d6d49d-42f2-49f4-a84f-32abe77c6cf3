<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Styling Demo - Better Interest</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #000000, #1a1a1a);
            min-height: 100vh;
            color: #f5e6a3;
            font-family: system-ui, -apple-system, sans-serif;
        }
        
        .demo-card {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1.5px solid rgba(255, 193, 7, 0.8);
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 10px 25px -3px rgba(255, 193, 7, 0.1);
        }
        
        .demo-button {
            background: linear-gradient(to right, #fbbf24, #f59e0b);
            color: #000;
            border: 1.8px solid rgba(255, 193, 7, 0.8);
            border-radius: 40px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .demo-button:hover {
            transform: scale(0.98);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.25), inset 0 -1px 0 rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .demo-button:active {
            transform: scale(0.96);
            box-shadow: inset 0 4px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.05);
        }
        
        .demo-button-outline {
            background: transparent;
            color: #fbbf24;
            border: 1.8px solid rgba(255, 193, 7, 0.8);
            border-radius: 40px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .demo-button-outline:hover {
            background: rgba(255, 193, 7, 0.1);
            transform: scale(0.98);
        }
        
        .demo-input {
            background: rgba(0, 0, 0, 0.6);
            border: 1.5px solid rgba(255, 193, 7, 0.6);
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            color: #f5e6a3;
            width: 100%;
            transition: all 0.2s;
        }
        
        .demo-input:focus {
            outline: none;
            border-color: rgba(255, 193, 7, 0.8);
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
        }
        
        .demo-input::placeholder {
            color: rgba(245, 230, 163, 0.6);
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .logo-circle {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            overflow: hidden;
            background: rgba(255, 193, 7, 0.1);
            backdrop-filter: blur(10px);
            border: 1.5px solid rgba(255, 193, 7, 0.8);
            box-shadow: 0 4px 16px rgba(255, 193, 7, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-circle img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <div class="text-center mb-8">
            <div class="logo-container justify-center">
                <div class="logo-circle">
                    <img src="/logo-white-circular.svg" alt="Better Interest Logo">
                </div>
                <h1 class="text-3xl font-bold text-yellow-400">Better Interest</h1>
            </div>
            <p class="text-lg opacity-90">New Black Theme with Yellow-Orange Styling</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4 text-yellow-400">✅ Styling Updates Applied</h2>
            <ul class="space-y-2 text-sm">
                <li>🔲 <strong>Card Borders</strong> - All cards now have 1.5px borders</li>
                <li>🔘 <strong>Button Radius</strong> - All buttons use 40px border radius</li>
                <li>🎨 <strong>Black Theme</strong> - Pure black background with yellow-orange accents</li>
                <li>📝 <strong>Form Elements</strong> - Enhanced with 1.5px borders and yellow focus states</li>
                <li>✨ <strong>Dark Shadows</strong> - Yellow-tinted shadows for better depth</li>
                <li>🌟 <strong>Consistent Borders</strong> - 1.8px yellow-orange borders throughout</li>
            </ul>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4 text-yellow-400">🔘 Button Styles (40px Radius)</h2>
            <div class="flex gap-3 flex-wrap">
                <button class="demo-button">Primary Button</button>
                <button class="demo-button" style="background: linear-gradient(to right, #f59e0b, #dc2626);">Destructive</button>
                <button class="demo-button-outline">Outline Button</button>
            </div>
            <p class="text-xs mt-3 opacity-75">All buttons now use 40px border radius with 1.8px borders</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4 text-yellow-400">📝 Form Elements (1.5px Borders)</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2 text-yellow-400">Input Field</label>
                    <input type="text" class="demo-input" placeholder="Enter your text here...">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2 text-yellow-400">Textarea</label>
                    <textarea class="demo-input" rows="3" placeholder="Enter your message..."></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2 text-yellow-400">Select Dropdown</label>
                    <select class="demo-input">
                        <option>Choose an option...</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </div>
            <p class="text-xs mt-3 opacity-75">All form elements use 1.5px yellow borders with enhanced focus states</p>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4 text-yellow-400">🎨 Black Theme Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="demo-card" style="background: rgba(0, 0, 0, 0.9);">
                    <h3 class="text-lg font-semibold text-yellow-400 mb-2">Color Palette</h3>
                    <ul class="text-sm space-y-1">
                        <li>• <span class="text-yellow-400">Background:</span> Pure Black (#000000)</li>
                        <li>• <span class="text-yellow-400">Cards:</span> Dark Black (#0d0d0d)</li>
                        <li>• <span class="text-yellow-400">Borders:</span> Yellow-Orange (#fbbf24)</li>
                        <li>• <span class="text-yellow-400">Text:</span> Warm White (#f5e6a3)</li>
                    </ul>
                </div>
                <div class="demo-card" style="background: rgba(0, 0, 0, 0.9);">
                    <h3 class="text-lg font-semibold text-yellow-400 mb-2">Visual Effects</h3>
                    <ul class="text-sm space-y-1">
                        <li>• Yellow-tinted shadows</li>
                        <li>• Glassmorphism effects</li>
                        <li>• Enhanced contrast</li>
                        <li>• Smooth transitions</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2 class="text-xl font-semibold mb-4 text-yellow-400">📱 Component Updates</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="demo-card" style="background: rgba(0, 0, 0, 0.9); border-color: rgba(255, 193, 7, 0.8);">
                    <h4 class="font-semibold text-yellow-400">Cards</h4>
                    <p class="text-xs mt-1">1.5px yellow borders</p>
                </div>
                <div class="demo-card" style="background: rgba(0, 0, 0, 0.9); border-color: rgba(255, 193, 7, 0.8);">
                    <h4 class="font-semibold text-yellow-400">Buttons</h4>
                    <p class="text-xs mt-1">40px radius, 1.8px borders</p>
                </div>
                <div class="demo-card" style="background: rgba(0, 0, 0, 0.9); border-color: rgba(255, 193, 7, 0.8);">
                    <h4 class="font-semibold text-yellow-400">Forms</h4>
                    <p class="text-xs mt-1">1.5px borders, yellow focus</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-8">
            <p class="text-sm opacity-75">
                🚀 Your app now features a sleek black theme with yellow-orange accents!<br>
                Visit <a href="http://localhost:3000/" class="underline hover:text-yellow-300">http://localhost:3000/</a> to see the changes.
            </p>
        </div>
    </div>
</body>
</html>
