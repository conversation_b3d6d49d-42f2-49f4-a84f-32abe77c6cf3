/**
 * Environment configuration for Better Interest Savings App
 * Centralizes all environment variable access for Vite
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001',
  TIMEOUT: 30000, // 30 seconds
} as const;

// Paystack Configuration
export const PAYSTACK_CONFIG = {
  PUBLIC_KEY: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || '',
  BASE_URL: import.meta.env.VITE_PAYSTACK_BASE_URL || 'https://api.paystack.co',
} as const;

// App Configuration
export const APP_CONFIG = {
  NAME: import.meta.env.VITE_APP_NAME || 'Better Interest',
  VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  ENVIRONMENT: import.meta.env.VITE_APP_ENVIRONMENT || 'development',
} as const;

// Development Configuration
export const DEV_CONFIG = {
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
} as const;

// Validation functions
export const validateConfig = () => {
  const errors: string[] = [];

  if (!PAYSTACK_CONFIG.PUBLIC_KEY) {
    errors.push('VITE_PAYSTACK_PUBLIC_KEY is required');
  }

  if (!API_CONFIG.BASE_URL) {
    errors.push('VITE_API_URL is required');
  }

  if (errors.length > 0) {
    console.error('Environment configuration errors:', errors);
    if (APP_CONFIG.ENVIRONMENT === 'production') {
      throw new Error(`Missing required environment variables: ${errors.join(', ')}`);
    }
  }

  return errors.length === 0;
};

// Helper functions
export const isProduction = () => APP_CONFIG.ENVIRONMENT === 'production';
export const isDevelopment = () => APP_CONFIG.ENVIRONMENT === 'development';
export const isDebugMode = () => DEV_CONFIG.DEBUG;

// Export all configs as a single object for convenience
export const CONFIG = {
  API: API_CONFIG,
  PAYSTACK: PAYSTACK_CONFIG,
  APP: APP_CONFIG,
  DEV: DEV_CONFIG,
} as const;

export default CONFIG;
