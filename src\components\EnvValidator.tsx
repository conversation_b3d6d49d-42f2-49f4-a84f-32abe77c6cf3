import { useEffect, useState } from 'react';
import { validateConfig, CONFIG, isDebugMode } from '@/config/env';
import { toast } from 'sonner';

interface EnvValidatorProps {
  children: React.ReactNode;
}

export const EnvValidator = ({ children }: EnvValidatorProps) => {
  const [isValid, setIsValid] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkConfig = () => {
      try {
        const isConfigValid = validateConfig();
        
        if (isDebugMode()) {
          console.log('Environment Configuration:', {
            API_URL: CONFIG.API.BASE_URL,
            PAYSTACK_PUBLIC_KEY: CONFIG.PAYSTACK.PUBLIC_KEY ? '***configured***' : 'missing',
            APP_NAME: CONFIG.APP.NAME,
            ENVIRONMENT: CONFIG.APP.ENVIRONMENT,
          });
        }

        if (!isConfigValid) {
          toast.error('Configuration Error: Some environment variables are missing. Check console for details.');
        }

        setIsValid(isConfigValid);
      } catch (error) {
        console.error('Environment validation failed:', error);
        toast.error('Critical configuration error. Please check environment variables.');
        setIsValid(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkConfig();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 to-secondary/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing application...</p>
        </div>
      </div>
    );
  }

  if (!isValid && CONFIG.APP.ENVIRONMENT === 'production') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-destructive/10 to-destructive/20">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-destructive text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-destructive mb-2">Configuration Error</h1>
          <p className="text-muted-foreground mb-4">
            The application is not properly configured. Please contact support.
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default EnvValidator;
