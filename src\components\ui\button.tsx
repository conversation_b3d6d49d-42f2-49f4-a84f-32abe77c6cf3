import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-b from-primary via-primary to-primary/90 text-primary-foreground rounded-[40px] border-[1.8px] border-primary/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15),inset_0_-1px_0_rgba(0,0,0,0.15),0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25),inset_0_-1px_0_rgba(255,255,255,0.1),0_2px_4px_rgba(0,0,0,0.2)] active:shadow-[inset_0_4px_8px_rgba(0,0,0,0.3),inset_0_1px_0_rgba(255,255,255,0.05)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 group dark:border-green-500/60",
        destructive: "bg-gradient-to-b from-destructive via-destructive to-destructive/90 text-destructive-foreground rounded-[40px] border-[1.8px] border-destructive/20 shadow-[inset_0_1px_0_rgba(255,255,255,0.15),inset_0_-1px_0_rgba(0,0,0,0.15),0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-red-500/60",
        outline: "bg-gradient-to-b from-background via-background to-muted/50 border-[1.8px] border-primary/40 text-foreground rounded-[40px] shadow-[inset_0_1px_0_rgba(255,255,255,0.8),inset_0_-1px_0_rgba(0,0,0,0.1),0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)] hover:bg-accent hover:text-accent-foreground hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-green-500/60",
        secondary: "bg-gradient-to-b from-secondary via-secondary to-secondary/90 text-secondary-foreground rounded-[40px] border-[1.8px] border-secondary/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15),inset_0_-1px_0_rgba(0,0,0,0.15),0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-yellow-500/60",
        ghost: "hover:bg-accent hover:text-accent-foreground rounded-[40px] hover:scale-[0.98] transition-all duration-200 border-[1.8px] border-transparent dark:hover:border-green-500/40",
        link: "text-primary underline-offset-4 hover:underline rounded-[40px] transition-all duration-200",
        brand: "bg-primary text-primary-foreground hover:bg-primary/90 rounded-[40px] border-[1.8px] border-primary/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-green-500/60",
        accent: "bg-gradient-to-b from-accent to-accent/80 text-accent-foreground rounded-[40px] border-[1.8px] border-accent/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-orange-500/60",
        glow: "bg-gradient-to-b from-primary to-primary/80 text-primary-foreground rounded-[40px] border-[1.8px] border-primary/40 shadow-lg hover:shadow-xl hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-green-500/60 dark:shadow-green-500/20",
        admin: "bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-[40px] border-[1.8px] border-secondary/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-yellow-500/60",
        yellow: "bg-secondary text-secondary-foreground hover:bg-secondary/90 font-semibold rounded-[40px] border-[1.8px] border-secondary/30 shadow-[inset_0_1px_0_rgba(255,255,255,0.15)] hover:shadow-[inset_0_2px_4px_rgba(0,0,0,0.25)] hover:scale-[0.98] active:scale-[0.96] transition-all duration-200 dark:border-yellow-500/60"
      },
      size: {
        default: "h-12 px-6 py-3 text-sm gap-3",
        sm: "h-10 px-4 py-2 text-xs gap-2",
        lg: "h-14 px-8 py-4 text-base gap-4",
        icon: "h-12 w-12 p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      >
        <span className="flex items-center justify-between w-full gap-2">
          <span className="flex items-center gap-2">
            {children}
          </span>
          <div className="h-6 w-6 rounded-full bg-white/20 flex items-center justify-center ml-auto group-hover:bg-white/30 transition-colors duration-200">
            <svg 
              className="h-3 w-3 text-current opacity-80" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2.5} 
                d="M9 5l7 7-7 7" 
              />
            </svg>
          </div>
        </span>
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
