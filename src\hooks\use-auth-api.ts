import { API_CONFIG } from '@/config/env';
import axios from 'axios';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: string;
  kycStatus: string;
  isAdmin: boolean;
  profile: {
    first_name: string;
    last_name: string;
    status: string;
    kyc_status: string;
  };
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
  message: string;
}

const API_URL = API_CONFIG.BASE_URL;

console.log('🔐 Auth API Configuration:', {
  API_URL,
  BACKEND_URL: API_CONFIG.BACKEND_URL
});

export const useAuthApi = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getToken = () => localStorage.getItem('token');

  // Login user
  const login = useCallback(async (credentials: LoginRequest) => {
    setIsLoading(true);
    setError(null);
    
    console.log('🔐 Attempting login:', {
      email: credentials.email,
      url: `${API_URL}/auth/login`
    });

    try {
      const response = await axios.post(`${API_URL}/auth/login`, credentials);
      
      console.log('✅ Login successful:', {
        status: response.status,
        data: response.data
      });

      const { user, token } = response.data;
      
      // Store auth data
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      
      toast.success('Login successful!');
      return { data: { user, token }, error: null };
    } catch (error) {
      console.error('❌ Login error:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Login failed';
      
      setError(message);
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register user
  const register = useCallback(async (userData: RegisterRequest) => {
    setIsLoading(true);
    setError(null);
    
    console.log('📝 Attempting registration:', {
      email: userData.email,
      url: `${API_URL}/auth/register`
    });

    try {
      const response = await axios.post(`${API_URL}/auth/register`, userData);
      
      console.log('✅ Registration successful:', {
        status: response.status,
        data: response.data
      });

      const { user, token } = response.data;
      
      // Store auth data
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      
      toast.success('Account created successfully!');
      return { data: { user, token }, error: null };
    } catch (error) {
      console.error('❌ Registration error:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Registration failed';
      
      setError(message);
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get user profile
  const getProfile = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    console.log('👤 Fetching user profile:', {
      url: `${API_URL}/auth/profile`
    });

    try {
      const token = getToken();
      if (!token) throw new Error('No authentication token');

      const response = await axios.get(`${API_URL}/auth/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Profile fetched:', {
        status: response.status,
        data: response.data
      });

      return { data: response.data, error: null };
    } catch (error) {
      console.error('❌ Profile fetch error:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch profile';
      
      setError(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user profile
  const updateProfile = useCallback(async (profileData: Partial<AuthUser>) => {
    setIsLoading(true);
    setError(null);
    
    console.log('📝 Updating user profile:', {
      url: `${API_URL}/auth/profile`,
      data: profileData
    });

    try {
      const token = getToken();
      if (!token) throw new Error('No authentication token');

      const response = await axios.put(`${API_URL}/auth/profile`, profileData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Profile updated:', {
        status: response.status,
        data: response.data
      });

      // Update stored user data
      const updatedUser = response.data;
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      toast.success('Profile updated successfully!');
      return { data: updatedUser, error: null };
    } catch (error) {
      console.error('❌ Profile update error:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to update profile';
      
      setError(message);
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Change password
  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    setIsLoading(true);
    setError(null);
    
    console.log('🔒 Changing password:', {
      url: `${API_URL}/auth/change-password`
    });

    try {
      const token = getToken();
      if (!token) throw new Error('No authentication token');

      const response = await axios.put(`${API_URL}/auth/change-password`, {
        currentPassword,
        newPassword
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Password changed:', {
        status: response.status
      });

      toast.success('Password changed successfully!');
      return { data: response.data, error: null };
    } catch (error) {
      console.error('❌ Password change error:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to change password';
      
      setError(message);
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout
  const logout = useCallback(async () => {
    console.log('🚪 Logging out user');
    
    try {
      const token = getToken();
      if (token) {
        // Optional: Call logout endpoint
        await axios.post(`${API_URL}/auth/logout`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }
    } catch (error) {
      console.warn('⚠️ Logout endpoint error (continuing anyway):', error);
    }
    
    // Clear local storage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    toast.success('Logged out successfully!');
  }, []);

  return {
    login,
    register,
    getProfile,
    updateProfile,
    changePassword,
    logout,
    isLoading,
    error
  };
};
